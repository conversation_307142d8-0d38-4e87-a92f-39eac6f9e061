import * as dotenv from 'dotenv';

dotenv.config();

const CHANNEL_MESSAGES_SEARCH_SIZE: string = process.env['CHANNEL_MESSAGES_SEARCH_SIZE'] ?? '50';

interface TeamsChannelRequest {
  id: string;
  method: string;
  url: string;
}

export function createChannelMessagesRequests(teamId: string, channelId: string): TeamsChannelRequest[] {
  if (!teamId) return [];

  return [{
    id: `${channelId}`,
    method: 'GET',
    url: `/teams/${teamId}/channels/${channelId}/messages?$top=${CHANNEL_MESSAGES_SEARCH_SIZE}`
  }];
}

export function createChannelsMembersRequests(teamId: string, channelId: string): TeamsChannelRequest[] {
  if (!teamId) return [];

  return [{
    id: `${channelId}`,
    method: 'GET',
    url: `/teams/${teamId}/channels/${channelId}/members`
  }];
}