import { Entity, NullableOption, PublicError } from '@microsoft/microsoft-graph-types';

export type ValueOf<T> = T[keyof T];

/**
 * Graph API Batchリクエストのレスポンス1件分
 */
export interface IBatchResponseData {
  id?: string,
  status?: number,
  headers?: NullableOption<{ 'Retry-After'?: string, 'Content-Type'?: string }>
  body?: {
    value?: Entity[],
    error?: NullableOption<PublicError>,
  },
}

/**
 * Graph API Batchリクエストのレスポンス
 * Ref: BatchResponseBody
 */
export interface IBatchResponses {
  "@odata.nextLink"?: string;
  responses?: IBatchResponseData[],
}

export interface IUsersChatsTableStorageData {
  countId: number;
  userId: string;
  chatId: string;
  chatType: string;
}
export interface IChatUser {
  id: string;
  status: string;
}
export interface IModifiedUsersChatsData {
  countId: number;
  chatId: string;
  chatType: string;
  users: IChatUser[];
  userCount: number;
}

export interface IUniqueChats {
  userId: string;
  chatId: string;
  chatType: string;
}
export interface IUniqueMembers {
  userId: string;
}

export interface ITeamsChatsMessages {
  security_user_id: string[];
  id: string;
  kind: string;
  replyToId: string | null;
  messageType: string | null;
  createdDateTime: string | null;
  lastModifiedDateTime: string | null;
  lastEditedDateTime: string | null;
  deletedDateTime: string | null;
  subject: string | null;
  chatId: string | null;
  from: IMessageFrom | null;
  body: IMessageBody | null;
  hasAttachments: boolean;
  channelIdentity: IChannelIdentity | null;
  softDelete: boolean;
}
export interface IMessageFrom {
  application: IMessageApplication | null;
  device: IMessageDevice | null;
  user: IMessageUser | null;
}
export interface IMessageApplication {
  displayName: string;
}
export interface IMessageDevice {
  displayName: string;
}
export interface IMessageUser {
  displayName: string;
}
export interface IMessageBody {
  content: string;
}
export interface IChannelIdentity {
  teamId: string ;
  channelId: string;
}
/**
 * Teams Chat Members
 */
export interface ITeamsChatsMembers {
  id: string;
  displayName: string;
  visibleHistoryStartDateTime: string | null;
  userId: string | null;
  email: string | null;
}